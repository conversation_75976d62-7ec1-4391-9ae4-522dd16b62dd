export default {
  name: 'usps-sites-report',
  props: ['selectedRange'],
  data() {
    return {
        loading: false,
        sites: [],
        suppliersUniqueValues: [],
        snackbar: { snackbar: false, text: '', timeout: 3000, x: 'right', y: 'top' },
        tabulatorConfigSitesReport: {
            data: [],
            reactiveData: false,
            movableColumns: false,
            movableRows: false,
            persistence: {
                sort: true,
                filter: true,
                columns: true
            },
            printStyled: true,
            height: "100%",
            resizableRows: false,
            downloadRowRange: "selected",
            layout: "fitColumns",
            filterMode: "local",
            sortMode: "local",
            paginationMode: "local",
            pagination: true,
            paginationSize: 1000,
            persistenceID: "reportsPage-uspsSitesReport",
            paginationSizeSelector: [100, 250, 500, 1000, true],
            paginationCounter: "rows",
            placeholder: "No Data Available",
            placeholderHeaderFilter: "No Matching Data",
            initialSort: [
                { column: "site", dir: "asc" }
            ],
            columns: [
                { title: "Site", field: "site", headerHozAlign: "center", width: 200, headerFilter: "input" },
                { title: "Address", field: "address", headerHozAlign: "center", width: 200, headerFilter: "input" },
                { title: "City", field: "city", headerHozAlign: "center", width: 150, headerFilter: "list", headerFilterParams: { valuesLookup: true, clearable: true, multiselect: true } },
                { title: "State", field: "state", headerHozAlign: "center", width: 100, headerFilter: "list", headerFilterParams: { valuesLookup: true, clearable: true, multiselect: true } },
                { title: "Zip", field: "zip", headerHozAlign: "center", width: 100, headerFilter: "input" },
                { title: "IFN", field: "ifn", headerHozAlign: "center", width: 100, headerFilter: "input" },
                { title: "Supplier", field: "supplier", headerHozAlign: "center", width: 120, headerFilter: "list", 
                  headerFilterParams: { valuesLookup: () => this.suppliersUniqueValues, clearable: true, multiselect: true },
                  headerFilterFunc: (headerValue, rowValue, rowData) => {
                    const suppliers = rowData.supplier ? rowData.supplier.split(',') : [];
                    return headerValue.find(item => suppliers.includes(item));
                  },
                  headerFilterEmptyCheck: (value) => {
                    return value.length == 0;
                  }
                },
                { title: "Region", field: "region", headerHozAlign: "center", width: 120, headerFilter: "list", headerFilterParams: { valuesLookup: true, clearable: true, multiselect: true } },
                { title: "Holidays", field: "natlHolidaysCount", headerHozAlign: "center", hozAlign: "center", width: 140, headerFilter: "input" },
                { title: "Expected", field: "expected", headerHozAlign: "center", hozAlign: "center", width: 120, headerFilter: "input" },
                { title: "Sitefotos Visits", field: "sitefotosVisits", headerHozAlign: "center", hozAlign: "center", width: 180, headerFilter: "input" },
                { title: "Missed", field: "missedCount", headerHozAlign: "center", hozAlign: "center", width: 120, headerFilter: "input" },
                { title: "Consecutive Missed", field: "consecutiveMissedCount", headerHozAlign: "center", hozAlign: "center", width: 200, headerFilter: "input" }
            ]
        }
    };
  },
  computed: {
    sDate() {
      if (!this.selectedRange) return null;
      const [startDateUnix] = this.selectedRange.split(',');
      return parseInt(startDateUnix);
    },
    eDate() {
      if (!this.selectedRange) return null;
      const [, endDateUnix] = this.selectedRange.split(',');      
      return parseInt(endDateUnix);
    }
  },
  watch: {
    selectedRange: 'fetchData',
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      if (!this.sDate || !this.eDate) return;
      this.loading = true;
      try {
        // Replace with your actual API endpoint and params
        const res = await axios.get('/node/reports/usps-sites-report', {
            params: { start: this.sDate, end: this.eDate }
        });
        this.sites = res.data;
        this.suppliersUniqueValues = [];
        for (const data of res.data) {
          // Supplier is comma separated value
          if (data.supplier) {
            //Convert supplier value to upper case
            data.supplier = data.supplier.toUpperCase();
            const suppliers = data.supplier.split(',').map(s => s.trim());
            for (const supplier of suppliers) {
              if (!this.suppliersUniqueValues.includes(supplier)) {
                this.suppliersUniqueValues.push(supplier);
              }
            }
          }
        }
        this.tabulatorConfigSitesReport.data = res.data;
        const tabulatorRef = this.$refs.sitesReport?.tabulator;
        if (tabulatorRef && typeof tabulatorRef.setData === 'function') {
            tabulatorRef.setData(res.data);
        }
      } catch (e) {

        const errorData = typeof e.response.data === 'string' ? JSON.parse(e.response.data) : e.response.data;
        let errorMessage = 'Failed to load data';
        if (errorData && errorData.message) {
            errorMessage = errorData.message;
        }
        this.snackbar = { snackbar: true, text: errorMessage, timeout: 3000, x: 'left', y: 'bottom' };
      } finally {
        this.loading = false;
      }
    },
  },
  template: /*HTML*/`<v-card light class="mx-2">
    <v-container fluid class="pa-0">
      <div ref="mainBox" class="elevation-0 ">
        <template v-if="loading">
          <v-progress-linear color="blue" indeterminate></v-progress-linear>
        </template>
        <tabulator-datatable title="Sites Report" :tabulator-configuration="tabulatorConfigSitesReport" refresh-type="local" :show-refresh-button="false" :show-clear-all-filters-button="true" id="reportsPage-sitesReport" ref="sitesReport" :show-primary-button="false" :show-download-excel-button="true" :show-download-pdf-button="true" :name-of-file-export="'Sites Report (' + sDate + ' - ' + eDate + ')'" />
      </div>
    </v-container>
    <v-snackbar v-model="snackbar.snackbar" :timeout="snackbar.timeout" :top="snackbar.y === 'top'" :right="snackbar.x === 'right'">
      {{ snackbar.text }}
      <v-btn color="pink" text @click="snackbar.snackbar = false">Close</v-btn>
    </v-snackbar>
  </v-card>`
};

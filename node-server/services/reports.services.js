const mysql = require('../utils/db');
let PHPUnserialize = require('php-unserialize');
const { redisClient } = require('../utils/redis-client.js');
const Holidays = require('date-holidays');

/**
 * Returns holidays in the same format as the old fetchHolidays for a given date range (unix seconds).
 */
function fetchHolidays(startTimestamp, endTimestamp, country = 'US') {
    const hd = new Holidays(country);
    const start = new Date(Number(startTimestamp) * 1000);
    const end = new Date(Number(endTimestamp) * 1000);
    let holidays = [];
    for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
        holidays = holidays.concat(hd.getHolidays(year));
    }
    holidays = holidays.filter(h => {
        const d = new Date(h.date);
        return d >= start && d <= end;
    });
    return holidays;
}

const getUSPSSitesReport = async (internalUid, startTimestamp, endTimestamp) => {
    const holidays = fetchHolidays(startTimestamp, endTimestamp);
    // Build a Set of holiday date strings (YYYY-MM-DD)
    const holidayDatesSet = new Set(
        holidays.map(h => { 
            const d = new Date(h.date); 
            return d.toISOString().slice(0, 10); 
        })
    );
    const siteIds = Array.from(new Set((await mysql.awaitSafeQuery(`select ssc_data from sitefotos_subuser_configuration where ssc_user_id=?`, [internalUid])).map(row => row.ssc_data.map(bldg => parseInt(bldg))).flat()));


    if (siteIds.length === 0) {
        throw new Error('No sites found for this user');
    }
    // Fetch additional columns for each site: address, zip, district, nickname, etc.
    const sitesDetails = await mysql.awaitSafeQuery(
        `SELECT 
        mb.mb_id, 
        mb.mb_nickname, 
        mb.mb_address1, 
        (SELECT City FROM maptile_city WHERE CityId = mb.mb_city) AS mb_city, 
        (SELECT state_abv_name FROM maptile_state WHERE id = mb.mb_state) AS mb_state, 
        mb.mb_zip_code,
        (
          SELECT GROUP_CONCAT(
            CASE 
              WHEN mv.vendor_company_name IS NOT NULL AND mv.vendor_company_name != '' 
              THEN mv.vendor_company_name 
              ELSE CONCAT(mv.vendor_fname, ' ', mv.vendor_lname) 
            END
            SEPARATOR ', '
          )
          FROM sitefotos_site_client_mapping sscm
          LEFT JOIN maptile_vendors mv ON mv.vendor_id = sscm.sscm_vendor_id
          WHERE sscm.sscm_site_id = mb.mb_id
        ) AS supplier,
        (
          SELECT scs.scs_client_internal_id
          FROM sitefotos_site_client_mapping sscm
          LEFT JOIN sitefotos_client_sites scs ON scs.scs_id = sscm.sscm_client_site_id
          WHERE sscm.sscm_site_id = mb.mb_id
          LIMIT 1
        ) AS ifn,
         (
          SELECT scsd_region
          FROM sitefotos_site_client_mapping sscm
          left join sitefotos_client_sites_data on scsd_client_site_id = sscm_client_site_id
          WHERE sscm.sscm_site_id = mb.mb_id
          LIMIT 1
        ) AS region
      FROM maptile_building mb
      WHERE mb.mb_id IN (${siteIds.map(() => '?').join(',')})`,
        [...siteIds]
    );
    // Find the form where its name begins with 'USPS'
    // Find forms where sf_form_name starts with 'USPS' and sf_form_site JSON contains any of the siteIds

    let siteForms = []
    if (siteIds.length > 0) {
        siteForms = await mysql.awaitSafeQuery(
            `SELECT sf_id, sf_form_name, sf_form_site, sf_form_data_app
         FROM sitefotos_forms
         WHERE sf_form_name LIKE 'USPS%' AND (${siteIds.map(() => "JSON_CONTAINS(sf_form_site, JSON_QUOTE(?))").join(" OR ")})`,
            siteIds.map(String)
        );
    }
    let formSubmissions = [];
    if (siteForms.length > 0) {
        formSubmissions = await mysql.awaitSafeQuery(
            `SELECT sfs_id, sfs_form_id, sfs_created, sfs_uploader_email
         FROM sitefotos_forms_submitted
         WHERE sfs_created >= ? AND sfs_created <= ? AND sfs_form_id IN (${siteForms.map(() => '?').join(',')})`,
            [startTimestamp, endTimestamp, ...siteForms.map(f => f.sf_id)]
        );
    }

    // Build a map of formId to siteId for quick lookup
    const siteFormMap = {};
    const formFormSubmissionMap = {};
    // Build a map of siteId to form for reporting
    for (const siteForm of siteForms) {
        const siteId = siteForm.sf_form_site ? JSON.parse(siteForm.sf_form_site)[0] : null;
        if (siteId) {
            siteFormMap[siteId] = { form: siteForm, submissions: [] };
            formFormSubmissionMap[siteForm.sf_id] = siteId;
        }
    }

    // Map submissions to their siteId using the formId->siteId mapping
    for (const submission of formSubmissions) {
        const siteId = formFormSubmissionMap[submission.sfs_form_id];
        if (siteId) {
            siteFormMap[siteId].submissions.push(submission);
        }
    }
    const result = [];
    for (const siteDetail of sitesDetails) {
        const siteForm = siteFormMap[siteDetail.mb_id];
        if (!siteForm) continue;

        // Calculate number of days the form should be submitted based on available days in form JSON
        let formJson = JSON.parse(siteForm.form.sf_form_data_app);

        // Extract available days from the radiogroup choices
        let expectedDays = [];
        if (formJson && Array.isArray(formJson.pages)) {
            for (const page of formJson.pages) {
                if (Array.isArray(page.elements)) {
                    for (const el of page.elements) {
                        if (el.type === 'radiogroup' && Array.isArray(el.choices)) {
                            expectedDays = el.choices.map(choice => choice.choiceValue);
                            break;
                        }
                    }
                }
                if (expectedDays.length) break;
            }
        }

        // Map day names to numbers (0=Sun, 1=Mon, ..., 6=Sat)
        const dayNameToNum = { Sun: 0, Mon: 1, Tue: 2, Wed: 3, Thu: 4, Fri: 5, Sat: 6 };
        const expectedDayNums = expectedDays.map(d => dayNameToNum[d]).filter(d => d !== undefined);

        // Count number of days in range that match expectedDayNums and are not holidays
        let expectedSubmissions = 0;
        let expectedDates = [];
        // Calculate actual submissions for that site, for that day (not on holidays)
        let actualSubmissions = 0;
        let seenDates = new Set();
        let missingSubmissionDates = [];
        let missingConsecutiveDays = [];
        // Build a map of date string (YYYY-MM-DD) to submission for fast lookup
        const submissionsByDate = {};
        for (const submission of siteForm.submissions) {
            const submissionDate = new Date(submission.sfs_created * 1000).toISOString().slice(0, 10);
            submissionsByDate[submissionDate] = submission;
        }
        if (expectedDayNums.length > 0) {
            let d = new Date(startTimestamp * 1000);
            const endDate = new Date(endTimestamp * 1000);
            let missingSubmissionDate = null;
            let advanceSubmissionDate = null;
            while (d <= endDate) {
                const dateStr = d.toISOString().slice(0, 10);
                if (expectedDayNums.includes(d.getDay()) && !holidayDatesSet.has(dateStr)) {
                    expectedSubmissions++;
                    expectedDates.push(dateStr);
                }
                if (expectedDayNums.includes(d.getDay())) {
                    const submission = submissionsByDate[dateStr];
                    if (submission) {
                        seenDates.add(dateStr);
                        actualSubmissions++;
                        missingSubmissionDate = null;
                    } else if (!holidayDatesSet.has(dateStr)) {
                        //Check if there is advanced submission
                        if (advanceSubmissionDate) {
                            // If there is an advanced submission, we can consider it as a valid submission
                            seenDates.add(dateStr);
                            actualSubmissions++;
                            advanceSubmissionDate = null;
                            missingSubmissionDate = null;
                        } else {
                            if (missingSubmissionDate) {
                                missingConsecutiveDays.push([missingSubmissionDate, dateStr]);
                            }
                            missingSubmissionDate = dateStr;
                            missingSubmissionDates.push(dateStr);
                        }
                    }
                } else {
                    const submission = submissionsByDate[dateStr];
                    if (submission) {
                        seenDates.add(missingSubmissionDate);
                        actualSubmissions++;
                        // If the date is not in the expected days then check if any last missing submissions occurred
                        if (missingSubmissionDate) {
                            //Remove missingSubmissionDate from the array
                            missingSubmissionDates = missingSubmissionDates.filter(date => date !== missingSubmissionDate);
                            missingSubmissionDate = null;
                        } else {
                            advanceSubmissionDate = dateStr;
                        }
                    }
                }
                d.setDate(d.getDate() + 1);
            }
        }    

        // Find holidays for this site in the date range
        result.push({
            site: siteDetail.mb_nickname || '',
            address: siteDetail.mb_address1 || '',
            city: siteDetail.mb_city || '',
            state: siteDetail.mb_state || '',
            zip: siteDetail.mb_zip_code || '',
            ifn: siteDetail.ifn || '',
            supplier: siteDetail.supplier || '',
            region: siteDetail.region || '',
            natlHolidaysCount: holidays.length,
            natlHolidays: holidays,
            expected: expectedSubmissions,
            sitefotosVisits: actualSubmissions,
            sitefotosVisitDates: Array.from(seenDates),
            missedCount: missingSubmissionDates.length,
            missed: missingSubmissionDates,
            consecutiveMissedCount: missingConsecutiveDays.length,
            consecutiveMissed: missingConsecutiveDays
        });
    }
    return result
}
const getPricingStructure = async(vendorId, contractFor) => {
    let results = await mysql.awaitQuery(`select * from sitefotos_pricing_structure 
         where sps_vendor_id = ? and sps_contract_active = '1' and sps_contract_for = ?`, [vendorId,contractFor]);
    return results;
}

const getContractorDetailsFromVendorTable = async (contractorId) => {
    let results = await mysql.awaitQuery(`select mv.vendor_id from maptile_vendors as mv
                inner join sitefotos_contacts as sf on mv.vendor_access_code = sf.sf_contact_contractorid
                where sf.sf_contact_id = ?`, [contractorId]);
    return results;
}
const getHybridUserSites = async (userId) => {
    const userDetails = await mysql.awaitSafeQuery(`Select modified_at, vuc_data from verizon_user_configuration where vuc_user_id=?`, [userId]);

    if (userDetails.length > 0) {
        const cacheKey = `getHybridUserSites:${userId}:${userDetails[0].modified_at}`;
        const cachedData = await redisClient.get(cacheKey);

        if (cachedData) return JSON.parse(cachedData);
        const sites = JSON.parse(PHPUnserialize.unserialize(userDetails[0].vuc_data));
        const buildingIds = sites.map((row) => row.mb_id);
        let siteDetails = await mysql.awaitQuery(`SELECT mb_nickname,mb_external_id, mb_external_src, mb_vendor_internal_id, mb_address1, mb_country, mb_min_zoom, mb_status, mb_zip_code,mb_lat,mb_long, (select City from maptile_city where CityId = mb_city) as cityname,  (select state_abv_name from maptile_state where id = mb_state ) as statename,GROUP_CONCAT(IFNULL(mb_contract_type, 'No Trade') order by mb_id separator ';') as mb_contract_type,mb_vendor_internal_id,GROUP_CONCAT(vendor_company_name order by mb_id separator ',') as contractors, GROUP_CONCAT(mb_id order by mb_id separator  ',') AS site_id from maptile_building left join maptile_vendors on mb_user_id = vendor_id   where mb_id in (?) group by mb_vendor_internal_id`, [buildingIds]);

        
        redisClient.set(cacheKey, JSON.stringify(siteDetails), { 'EX': 60 * 60 * 24 * 3 });
        return siteDetails;
    }
    return [];
}

const getPricingForAllContractors = async (vendorId, startDate, endDate) => {
    let results = await mysql.awaitQuery(`select
              swsd_id as service_id,
              mb_nickname as site_name,
              mb_id as site_id,
              swsd_service_name as service_name,
              from_unixtime(svc.swsd_date_time) as time,
              swsd_snow_inch as snow_inch,
              swsd_hours as hours,
              swsd_people as people,
              swsd_service_id as s_id,
              swsd_uploader_vid as contractor_vid,
              vendor_email as contractor_email,
              CONCAT(vendor_fname," ", vendor_lname) as contractor_name,
              vendor_company_name as company_name,
              vendor_company_logo as company_logo,
              vs_service_type as connected_service,
              vs_equipment_id as equipment_id,
              swsd_service_options as options
          from sitefotos_wp_services_data svc
              inner join maptile_building
                  on mb_id=svc.swsd_site_id
              inner join maptile_vendors
                  on vendor_id=svc.swsd_uploader_vid
              inner join vendor_services
                  on vs_service_id = svc.swsd_service_id
          where swsd_vendor_id=?
            and swsd_date_time >= ?
            and swsd_date_time < ?
          order by svc.swsd_date_time`, [vendorId, startDate, endDate]);

    return results;
}

const getDeicePrice = (serviceOptions, fullPrice, partialPrice) =>
{
    if(serviceOptions == null || serviceOptions == "null")
        return fullPrice;
    let data = PHPUnserialize.unserialize(serviceOptions)

    if(data.length>0)
    {
        if(data[0].toString().toLowerCase() == "full")
            return fullPrice;
        else if(data[0].toString().toLowerCase() == "partial")
            return partialPrice
        else
            return fullPrice
    }
    else
        return fullPrice
}

const getSnowDataOptions = (serviceOptions) =>
{
    if(serviceOptions == null || serviceOptions == "null"){
        serviceOptions = 'a:1:{i:0;s:3:"0-4";}';
    }
    serviceOptions = 'a:1:{i:0;s:3:"0-4";}'; //This needs to be removed
    let data = PHPUnserialize.unserialize(serviceOptions)[0].replace(/["]+/g,'').split('-')
    if(data.length > 1){
        return Math.min(data[1],data[0])
    }
    else
        return data[0];
}

const postFormToTableReportToGoogleSheets = async (vendorId, data, sheetName) => {
    let url = "https://script.google.com/macros/s/AKfycbx_4YGnEg2V5MSElJbyiAhdlsxqPy250tJOcZyh_GFFrTcpoiol-LdQfn-m0refz-jL/exec";
    console.log("***SErvice***", JSON.stringify(data))
    let postToGoogle = await fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "user_id": vendorId,
          "sheet_name": sheetName,
          "data": data
        })
    });
    postToGoogle = await postToGoogle.text();
    return postToGoogle;
}

const fetchSitesWithNoServicesInGivenTimePeriod = async (vendorId, startUnix, endUnix) =>{
    let results = await mysql.awaitSafeQuery(
      /*SQL*/`SELECT mb.mb_id AS SiteID, mb.mb_nickname AS Name, (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb.mb_zone_id) as SiteZone FROM maptile_building mb LEFT JOIN maptile_vendor_pics mvp ON mb.mb_id = mvp.mvp_building_id AND mvp.mvp_vendor_id = ? AND mvp.created > ? AND mvp.created < ? LEFT JOIN sitefotos_forms_submitted sfs ON mb.mb_id = sfs.sfs_building_id AND sfs.sfs_vendor_id = ? AND sfs.sfs_created > ? AND sfs.sfs_created < ? WHERE mb.mb_user_id = ? AND mb.mb_status = '1' AND mvp.mvp_building_id IS NULL AND sfs.sfs_building_id IS NULL;`
    , [vendorId, startUnix, endUnix, vendorId, startUnix, endUnix, vendorId]);
    return results;
}

const fetchWorkordersReport = async (vendorId, startUnix, endUnix, page, itemsPerPage, sort) =>{
    let sortBy = ['sfs_created'];
    let sortDesc = ['DESC']
    if (sort, sort.length > 0){
      sortBy = [sort[0].field];
      sortDesc = sort[0].dir.toUpperCase()
    }
    //Let's first get total count
    let count = await mysql.awaitSafeQuery(`select count(sfs.sfs_id) as count
        from sitefotos_forms_submitted sfs LEFT JOIN sitefotos_work_orders_submissions swos on swos.swos_form_submission_id = sfs.sfs_id LEFT JOIN sitefotos_work_orders swo ON swo.swo_id = swos.swos_workorder_id
        where sfs.sfs_vendor_id = ? and sfs.sfs_created > ? and sfs.sfs_created < ? and swo.swo_id is not null;`
    , [vendorId, startUnix, endUnix]);
    itemsPerPage = itemsPerPage > -1 ? itemsPerPage : count[0].count;
    let results = await mysql.awaitSafeQuery(`select (select sf_contact_company_name from sitefotos_contacts_company_view where sf_contact_id = mb_client) as Client,
               mb.mb_id as SiteID,
               mb.mb_nickname as SiteName,
               IF(swos_vendor_id = swos_submitter_vendor_id, 'SELF', ( select vendor_company_name from maptile_vendors where vendor_id = swos_submitter_vendor_id)) as Contractor,
               swo.swo_internal_id as InternalId,
               swo.swo_external_id as ExternalId,
               swo.swo_system_id as WorkOrderSystemId,
               swo.swo_schedule_datetime as ScheduledDate,
               sfs.sfs_created as FormSubmittedDate,
               swos.swos_checkout_datetime as LastWorkTime,
               swo.swo_id as WorkorderId,
               (select swos_name from sitefotos_work_orders_systems where swos_id=swo_system_id) as SystemName,
               swo.swo_nte as NTE,
               sf.sf_form_name as FormName,
               sfs.sfs_id as SubmittedFormID,
               sfs.sfs_id as FormSubmittedId,
               sfs.sfs_form_data_full as FormDataSubmitted  
            from sitefotos_forms_submitted sfs LEFT JOIN sitefotos_work_orders_submissions swos on swos.swos_form_submission_id = sfs.sfs_id LEFT JOIN sitefotos_work_orders swo ON swo.swo_id = swos.swos_workorder_id AND swos.swos_checkout_datetime = ( SELECT MAX(swos.swos_checkout_datetime) FROM sitefotos_work_orders_submissions swos2 WHERE swos2.swos_workorder_id = swo.swo_id ) LEFT JOIN maptile_building mb on sfs.sfs_building_id = mb.mb_id LEFT JOIN sitefotos_forms sf on sfs.sfs_form_id = sf.sf_id where sfs.sfs_vendor_id = ? and sfs.sfs_created > ? and sfs.sfs_created < ? and swo.swo_id is not null ORDER BY ${sortBy} ${sortDesc} limit ${itemsPerPage} offset ${itemsPerPage * (page - 1)};`, [vendorId, startUnix, endUnix]);
    return {
        last_page: Math.ceil(count[0].count / itemsPerPage),
        last_row: count[0].count,
        data: results
    };
}

const postToGoogle = async (url, user_id, sheet_name, data) => {
  let objToBePosted = {
    "user_id": user_id,
    "sheet_name": sheet_name,
    "data": data
  };

  let postToGoogle = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(objToBePosted)
  });
  postToGoogle = await postToGoogle.text();
  return postToGoogle;
}

const getChaseDashboard = async (internalUid, vendorId) =>{
    const buildingIds = Array.from(new Set((await mysql.awaitQuery(`select ssc_data from sitefotos_subuser_configuration where ssc_user_id=?`, [internalUid])).map(row => row.ssc_data.map(bldg => parseInt(bldg))).flat()));
    if(!buildingIds || buildingIds.length === 0){
        return {
            Landscape: { sites: 0, forms: 0 },
            Snow: { sites: 0, forms: 0 },
            Janitorial: { sites: 0, forms: 0 },
            HVAC: {sites: 0, forms: 0}
        }
    }
    console.log(buildingIds)

    const siteSql = `
        SELECT f.contract_type, COUNT(1) as count
        FROM ( SELECT 'Snow removal' AS contract_type
        UNION ALL SELECT 'Landscaping' 
        UNION ALL SELECT 'Janitorial services'
        UNION ALL SELECT 'HVAC installation and maintenance'
        ) f
        JOIN maptile_building c 
        ON FIND_IN_SET(binary f.contract_type,c.mb_contract_type)>0
        AND c.mb_id IN (?) AND mb_status='1'
        GROUP BY f.contract_type
    `;
    const sites = (await mysql.awaitQuery(siteSql, [buildingIds])).reduce((acc, cur) => ({ ...acc, [cur.contract_type]: { sites: cur.count } }), {});
    console.log(sites)
    const formsSql = `
        SELECT sf_form_contract_type, COUNT(DISTINCT sfs_building_id) count
        FROM sitefotos_forms_submitted 
        INNER JOIN sitefotos_forms ON sfs_form_id = sf_id AND sfs_time_stamp >= CURDATE() - INTERVAL 1 DAY
        WHERE sfs_building_id IN (?)
        GROUP BY sf_form_contract_type
    `;
    const forms = await mysql.awaitQuery(formsSql, [buildingIds]);
    Object.keys(sites).forEach(site => sites[site].forms = (forms.find(form => form.sf_form_contract_type === site) || { count: 0 }).count);
    console.log(sites)
    return sites;
}

module.exports = {
    getPricingStructure,
    getContractorDetailsFromVendorTable,
    getPricingForAllContractors,
    getDeicePrice,
    getSnowDataOptions,
    postFormToTableReportToGoogleSheets,
    fetchSitesWithNoServicesInGivenTimePeriod,
    fetchWorkordersReport,
    postToGoogle,
    getHybridUserSites,
    getChaseDashboard,
    getUSPSSitesReport
}